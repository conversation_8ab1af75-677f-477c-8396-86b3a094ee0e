{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "MassTransit": "Debug", "Hazelcast": "Debug", "FileProcessorApplication": "Debug", "Processor.File.FileProcessorApplication": "Debug", "Shared.Processor.Services.ProcessorService": "Debug", "Shared.Processor.Services.ProcessorHealthMonitor": "Debug", "Shared.Processor.Services.ProcessorHealthMetricsService": "Debug", "Shared.Processor.Services.ProcessorFlowMetricsService": "Debug", "Shared.Processor.Services.ProcessorFileMetricsService": "Debug", "Shared.Processor.MassTransit.Consumers.ExecuteActivityCommandConsumer": "Debug", "Shared.Services.CacheService": "Debug"}}, "ProcessorConfiguration": {"ProcessorId": "new-file-processor-v3", "Version": "1.1.1", "Name": "FileProcessor", "Description": "Enhanced file processor v3.0 with simplified AssignmentModel processing - no address/delivery specifics", "InputSchemaId": "30499a0f-f323-48b2-bfb2-8c51d67870e7", "OutputSchemaId": "30499a0f-f323-48b2-bfb2-8c51d67870e7"}, "RabbitMQ": {"Host": "localhost", "VirtualHost": "/", "Username": "guest", "Password": "guest", "RetryLimit": 3, "RetryInterval": "00:00:30", "PrefetchCount": 16, "ConcurrencyLimit": 10}, "OpenTelemetry": {"Endpoint": "http://localhost:4317", "UseConsoleExporter": true}, "Hazelcast": {"ClusterName": "EntitiesManager", "NetworkConfig": {"Addresses": ["127.0.0.1:5701"]}, "ConnectionTimeout": "00:00:30", "ConnectionRetryConfig": {"InitialBackoffMillis": 1000, "MaxBackoffMillis": 30000, "Multiplier": 2.0, "ClusterConnectTimeoutMillis": 20000, "JitterRatio": 0.2}}, "SchemaValidation": {"EnableInputValidation": false, "EnableOutputValidation": false, "FailOnValidationError": false, "LogValidationWarnings": true, "LogValidationErrors": true, "IncludeValidationTelemetry": true}, "ProcessorInitialization": {"RetryEndlessly": true, "RetryDelay": "00:00:05", "MaxRetryDelay": "00:01:00", "UseExponentialBackoff": true, "InitializationTimeout": "00:00:30", "LogRetryAttempts": true}, "ProcessorHealthMonitor": {"Enabled": true, "HealthCheckInterval": "00:00:30", "HealthCacheTtl": "00:05:00", "HealthCacheMapName": "processor-health", "IncludePerformanceMetrics": true, "IncludeDetailedHealthChecks": true, "MaxRetries": 5, "RetryDelay": "00:00:02", "LogHealthChecks": true, "LogLevel": "Information", "ContinueOnCacheFailure": false, "UseExponentialBackoff": true, "PerformanceMetrics": {"CollectCpuMetrics": true, "CollectMemoryMetrics": true, "CollectThroughputMetrics": true, "ThroughputWindow": "00:05:00", "CollectGcMetrics": true, "CollectThreadPoolMetrics": false}}, "HealthChecks": {"EvaluationTimeInSeconds": 10, "MinimumSecondsBetweenFailureNotifications": 60}}