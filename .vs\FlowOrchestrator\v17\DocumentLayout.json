{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Design49\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|c:\\users\\<USER>\\source\\repos\\design49\\processors\\processor.file\\fileprocessorapplication.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|solutionrelative:processors\\processor.file\\fileprocessorapplication.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AB87B138-D47F-4041-A44A-DACBC05068F6}|Shared\\Shared.Processor\\Shared.Processor.csproj|c:\\users\\<USER>\\source\\repos\\design49\\shared\\shared.processor\\baseprocessorapplication.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AB87B138-D47F-4041-A44A-DACBC05068F6}|Shared\\Shared.Processor\\Shared.Processor.csproj|solutionrelative:shared\\shared.processor\\baseprocessorapplication.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|c:\\users\\<USER>\\source\\repos\\design49\\processors\\processor.file\\services\\iprocessorfilemetricsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|solutionrelative:processors\\processor.file\\services\\iprocessorfilemetricsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AB87B138-D47F-4041-A44A-DACBC05068F6}|Shared\\Shared.Processor\\Shared.Processor.csproj|c:\\users\\<USER>\\source\\repos\\design49\\shared\\shared.processor\\masstransit\\consumers\\executeactivitycommandconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AB87B138-D47F-4041-A44A-DACBC05068F6}|Shared\\Shared.Processor\\Shared.Processor.csproj|solutionrelative:shared\\shared.processor\\masstransit\\consumers\\executeactivitycommandconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|c:\\users\\<USER>\\source\\repos\\design49\\processors\\processor.file\\services\\processorfilemetricsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|solutionrelative:processors\\processor.file\\services\\processorfilemetricsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AB87B138-D47F-4041-A44A-DACBC05068F6}|Shared\\Shared.Processor\\Shared.Processor.csproj|c:\\users\\<USER>\\source\\repos\\design49\\shared\\shared.processor\\services\\processorflowmetricsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AB87B138-D47F-4041-A44A-DACBC05068F6}|Shared\\Shared.Processor\\Shared.Processor.csproj|solutionrelative:shared\\shared.processor\\services\\processorflowmetricsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ProcessorFileMetricsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design49\\Processors\\Processor.File\\Services\\ProcessorFileMetricsService.cs", "RelativeDocumentMoniker": "Processors\\Processor.File\\Services\\ProcessorFileMetricsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design49\\Processors\\Processor.File\\Services\\ProcessorFileMetricsService.cs", "RelativeToolTip": "Processors\\Processor.File\\Services\\ProcessorFileMetricsService.cs", "ViewState": "AgIAACsAAAAAAAAAAAAQwAcAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:40:49.865Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "FileProcessorApplication.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design49\\Processors\\Processor.File\\FileProcessorApplication.cs", "RelativeDocumentMoniker": "Processors\\Processor.File\\FileProcessorApplication.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design49\\Processors\\Processor.File\\FileProcessorApplication.cs", "RelativeToolTip": "Processors\\Processor.File\\FileProcessorApplication.cs", "ViewState": "AgIAAG4AAAAAAAAAAAAIwLYAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:40:36.06Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "IProcessorFileMetricsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design49\\Processors\\Processor.File\\Services\\IProcessorFileMetricsService.cs", "RelativeDocumentMoniker": "Processors\\Processor.File\\Services\\IProcessorFileMetricsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design49\\Processors\\Processor.File\\Services\\IProcessorFileMetricsService.cs", "RelativeToolTip": "Processors\\Processor.File\\Services\\IProcessorFileMetricsService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:40:22.923Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ExecuteActivityCommandConsumer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design49\\Shared\\Shared.Processor\\MassTransit\\Consumers\\ExecuteActivityCommandConsumer.cs", "RelativeDocumentMoniker": "Shared\\Shared.Processor\\MassTransit\\Consumers\\ExecuteActivityCommandConsumer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design49\\Shared\\Shared.Processor\\MassTransit\\Consumers\\ExecuteActivityCommandConsumer.cs", "RelativeToolTip": "Shared\\Shared.Processor\\MassTransit\\Consumers\\ExecuteActivityCommandConsumer.cs", "ViewState": "AgIAAGQAAAAAAAAAAAAgwG8AAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:26:19.269Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ProcessorFlowMetricsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design49\\Shared\\Shared.Processor\\Services\\ProcessorFlowMetricsService.cs", "RelativeDocumentMoniker": "Shared\\Shared.Processor\\Services\\ProcessorFlowMetricsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design49\\Shared\\Shared.Processor\\Services\\ProcessorFlowMetricsService.cs", "RelativeToolTip": "Shared\\Shared.Processor\\Services\\ProcessorFlowMetricsService.cs", "ViewState": "AgIAAD0AAAAAAAAAAIAwwE0AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T10:13:29.222Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "BaseProcessorApplication.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design49\\Shared\\Shared.Processor\\BaseProcessorApplication.cs", "RelativeDocumentMoniker": "Shared\\Shared.Processor\\BaseProcessorApplication.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design49\\Shared\\Shared.Processor\\BaseProcessorApplication.cs", "RelativeToolTip": "Shared\\Shared.Processor\\BaseProcessorApplication.cs", "ViewState": "AgIAAEYCAAAAAAAAAAAlwGACAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T09:06:46.996Z", "EditorCaption": ""}]}]}]}